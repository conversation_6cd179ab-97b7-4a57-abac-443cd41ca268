{"name": "contract-assistant-server", "version": "1.0.0", "description": "合同助手后端(NestJS)", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "MIT", "scripts": {"start": "nest start --watch", "build": "nest build", "lint": "eslint 'src/**/*.ts' --fix", "format": "prettier --write 'src/**/*.ts'"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "diff": "^8.0.2", "docx": "^9.5.0", "jszip-sync": "^3.2.1-sync", "lodash": "^4.17.21", "openai": "^4.91.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pizzip": "^3.2.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sqlite3": "^5.1.7", "typeorm": "^0.3.19"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/testing": "^10.0.0", "@nestjs/typeorm": "^10.0.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/bcryptjs": "^2.4.6", "@types/diff": "^8.0.0", "@types/express": "^4.17.17", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/passport-jwt": "^4.0.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.1.3"}}