import { Injectable, Logger } from '@nestjs/common';

import * as diff from 'diff';
import * as fs from 'fs';
import * as path from 'path';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import { ContractHistoryService } from './contract-history.service';

/**
 * 合同下载服务
 * 负责根据sessionId生成并下载docx格式的合同文件
 */
@Injectable()
export class ContractDownloadService {
  private readonly logger = new Logger(ContractDownloadService.name);

  constructor(private readonly contractHistoryService: ContractHistoryService) {}

  /**
   * 根据sessionId下载合同docx文件
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 合同文件Buffer和文件名
   */
  async downloadContractBySessionId(
    userId: number,
    sessionId: string,
  ): Promise<ResultResponse<{ buffer: Buffer; filename: string }>> {
    return ResultUtil.execute(async () => {
      this.logger.log(`开始生成合同下载文件，用户ID: ${userId}, 会话ID: ${sessionId}`);

      // 1. 获取历史记录
      const historyResult = await this.contractHistoryService.findDetailBySessionId(
        userId,
        sessionId,
      );
      if (!historyResult.success || !historyResult.data) {
        return ResultUtil.fail('历史记录不存在或无权限访问', 'HISTORY_NOT_FOUND');
      }

      const history = historyResult.data;
      const { contractMarkdown, templateMarkdown, selectedTemplateName } = history;

      if (!contractMarkdown || !templateMarkdown || !selectedTemplateName) {
        return ResultUtil.fail('历史记录数据不完整', 'INCOMPLETE_HISTORY_DATA');
      }

      // 2. 查找对应的docx模板文件
      const templatePath = this.getTemplateDocxPath(selectedTemplateName);
      if (!templatePath || !fs.existsSync(templatePath)) {
        return ResultUtil.fail(
          `模板文件不存在: ${selectedTemplateName}`,
          'TEMPLATE_FILE_NOT_FOUND',
        );
      }

      // 3. 比较markdown差异
      const differences = this.compareMarkdownDifferences(templateMarkdown, contractMarkdown);
      this.logger.log(`发现 ${differences.length} 处差异需要应用到docx模板`);

      // 4. 生成修改后的docx文件
      const modifiedBuffer = await this.applyDifferencesToDocx(templatePath, differences);

      // 5. 生成文件名
      const filename = this.generateFilename(selectedTemplateName, sessionId);

      this.logger.log(`合同文件生成成功，文件名: ${filename}`);
      return { buffer: modifiedBuffer, filename };
    }, '生成合同下载文件失败');
  }

  /**
   * 获取模板docx文件路径
   * @param templateName 模板名称
   * @returns docx文件路径
   */
  private getTemplateDocxPath(templateName: string): string | null {
    try {
      // 移除.json后缀，添加.docx后缀
      let docxName = templateName.replace(/\.json$/, '.docx');
      // 如果不存在后缀,则补充
      if (docxName.indexOf('.docx') === -1) {
        docxName = `${docxName}.docx`;
      }
      const templatePath = path.join(process.cwd(), 'data', 'refers', docxName);

      this.logger.debug(`查找模板文件: ${templatePath}`);
      return templatePath;
    } catch (error) {
      this.logger.error(
        `获取模板路径失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * 比较两个markdown文件的差异
   * @param originalMarkdown 原始模板markdown
   * @param modifiedMarkdown 修改后的markdown
   * @returns 差异列表
   */
  private compareMarkdownDifferences(
    originalMarkdown: string,
    modifiedMarkdown: string,
  ): Array<{
    type: 'add' | 'remove' | 'modify';
    original: string;
    modified: string;
    context?: string;
  }> {
    const differences: Array<{
      type: 'add' | 'remove' | 'modify';
      original: string;
      modified: string;
      context?: string;
    }> = [];

    // 使用diff库进行逐行比较
    const changes = diff.diffLines(originalMarkdown, modifiedMarkdown);

    let lineNumber = 0;
    for (const change of changes) {
      if (change.added) {
        differences.push({
          type: 'add',
          original: '',
          modified: change.value.trim(),
          context: `行 ${lineNumber}`,
        });
      } else if (change.removed) {
        differences.push({
          type: 'remove',
          original: change.value.trim(),
          modified: '',
          context: `行 ${lineNumber}`,
        });
      } else {
        // 对于未改变的部分，检查是否有细微修改
        const originalLines = change.value.split('\n');
        const modifiedLines = change.value.split('\n');

        for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
          const originalLine = originalLines[i] || '';
          const modifiedLine = modifiedLines[i] || '';

          if (originalLine !== modifiedLine && originalLine.trim() && modifiedLine.trim()) {
            differences.push({
              type: 'modify',
              original: originalLine.trim(),
              modified: modifiedLine.trim(),
              context: `行 ${lineNumber + i}`,
            });
          }
        }
        lineNumber += originalLines.length;
      }
    }

    return differences;
  }

  /**
   * 将差异应用到docx文件
   * @param templatePath 模板文件路径
   * @param differences 差异列表
   * @returns 修改后的docx文件Buffer
   */
  private async applyDifferencesToDocx(
    templatePath: string,
    differences: Array<{
      type: 'add' | 'remove' | 'modify';
      original: string;
      modified: string;
      context?: string;
    }>,
  ): Promise<Buffer> {
    try {
      // 读取原始docx文件
      const templateBuffer = fs.readFileSync(templatePath);

      // 如果没有差异，直接返回原文件
      if (differences.length === 0) {
        this.logger.log('没有发现差异，返回原始模板文件');
        return templateBuffer;
      }

      // 使用PizZip解析docx文件
      const zip = new PizZip(templateBuffer);

      // 读取document.xml内容
      const documentXml = zip.file('word/document.xml')?.asText();
      if (!documentXml) {
        throw new Error('无法读取docx文件的document.xml');
      }

      // 应用文本替换
      let modifiedXml = documentXml;
      for (const diff of differences) {
        if (diff.type === 'modify' && diff.original && diff.modified) {
          // 标准化文本进行匹配
          const normalizedOriginal = this.normalizeText(diff.original);
          const normalizedModified = this.normalizeText(diff.modified);

          // 在XML中查找并替换文本
          modifiedXml = this.replaceTextInXml(modifiedXml, normalizedOriginal, normalizedModified);
        }
      }

      // 更新document.xml
      zip.file('word/document.xml', modifiedXml);

      // 生成新的docx文件
      const modifiedBuffer = zip.generate({ type: 'nodebuffer' });

      this.logger.log(`成功应用 ${differences.length} 处差异到docx文件`);
      return modifiedBuffer;
    } catch (error) {
      this.logger.error(
        `应用差异到docx文件失败: ${error instanceof Error ? error.message : String(error)}`,
      );

      // 如果处理失败，返回原始文件
      const fallbackBuffer = fs.readFileSync(templatePath);
      this.logger.warn('使用原始模板文件作为备选方案');
      return fallbackBuffer;
    }
  }

  /**
   * 标准化文本，处理全角/半角字符转换
   * @param text 原始文本
   * @returns 标准化后的文本
   */
  private normalizeText(text: string): string {
    return text
      .replace(/[""]/g, '"') // 统一引号
      .replace(/['']/g, "'") // 统一单引号
      .replace(/[，]/g, ',') // 统一逗号
      .replace(/[。]/g, '.') // 统一句号
      .replace(/[（）]/g, (match) => (match === '（' ? '(' : ')')) // 统一括号
      .replace(/[：]/g, ':') // 统一冒号
      .replace(/[；]/g, ';') // 统一分号
      .replace(/\s+/g, ' ') // 统一空格
      .trim();
  }

  /**
   * 在XML中替换文本内容
   * @param xml XML内容
   * @param searchText 要查找的文本
   * @param replaceText 替换的文本
   * @returns 替换后的XML
   */
  private replaceTextInXml(xml: string, searchText: string, replaceText: string): string {
    // 简单的文本替换策略
    // 注意：这是一个简化版本，实际应用中可能需要更复杂的XML解析和处理

    // 转义XML特殊字符
    const escapeXml = (text: string) => {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    };

    const escapedSearch = escapeXml(searchText);
    const escapedReplace = escapeXml(replaceText);

    // 尝试直接替换
    if (xml.includes(escapedSearch)) {
      return xml.replace(new RegExp(escapedSearch, 'g'), escapedReplace);
    }

    // 如果直接替换失败，尝试模糊匹配
    // 这里可以添加更复杂的匹配逻辑

    return xml;
  }

  /**
   * 生成下载文件名
   * @param templateName 模板名称
   * @param sessionId 会话ID
   * @returns 文件名
   */
  private generateFilename(templateName: string, sessionId: string): string {
    const baseName = templateName.replace(/\.json$/, '').replace(/【模板】/g, '');
    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const shortSessionId = sessionId.slice(-8); // 取sessionId后8位

    return `${baseName}_${timestamp}_${shortSessionId}.docx`;
  }
}
