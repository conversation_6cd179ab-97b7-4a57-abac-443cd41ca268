import { ApiProperty } from '@nestjs/swagger';

import { IsDateString, IsOptional, IsString } from 'class-validator';

import { PaginationQueryDto } from '../../common/dto/base-query.dto';

/**
 * 创建历史记录DTO
 */
export interface CreateContractHistoryDto {
  userInput: string;
  backendLogs: string;
  contractMarkdown: string;
  selectedTemplateName?: string;
  templateMarkdown?: string;
  userId?: number;
  sessionId?: string;
}

/**
 * 历史记录列表项DTO（用于列表显示，只包含必要字段）
 */
export class ContractHistoryListItemDto {
  @ApiProperty({ description: '历史记录ID' })
  id: number;

  @ApiProperty({ description: '历史记录标题' })
  title: string;

  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @ApiProperty({ description: '选择的模板名称' })
  selectedTemplateName: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 历史记录查询DTO（包含分页和过滤参数）
 */
export class HistoryQueryDto extends PaginationQueryDto {
  @ApiProperty({
    description: '标题关键词搜索（模糊匹配）',
    example: '数据保密',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: '合同内容关键词搜索（模糊匹配）',
    example: '甲方',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: '模板名称关键词搜索（模糊匹配）',
    example: '技术服务',
    required: false,
  })
  @IsOptional()
  @IsString()
  templateName?: string;

  @ApiProperty({
    description: '开始时间（ISO 8601格式）',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: '结束时间（ISO 8601格式）',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

/**
 * 更新历史记录标题DTO
 */
export class UpdateHistoryTitleDto {
  @ApiProperty({ description: '会话ID', example: 'session-123' })
  sessionId: string;

  @ApiProperty({ description: '新的标题', example: '软件开发合同', maxLength: 100 })
  title: string;
}
