import { ApiProperty } from '@nestjs/swagger';

import { Type } from 'class-transformer';
import { IsOptional, IsPositive, Max, Min } from 'class-validator';

/**
 * 分页查询DTO（保留原有接口，用于兼容）
 */
export class PaginationQueryDto {
  @ApiProperty({
    description: '页码，从1开始',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  page?: number = 1;

  @ApiProperty({
    description: '每页数量，最大100',
    example: 20,
    required: false,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Max(100)
  @Min(1)
  pageSize?: number = 20;
}
