-- 创建合同生成历史记录表
CREATE TABLE IF NOT EXISTS contract_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(100) NOT NULL,
    userInput TEXT NOT NULL,
    backendLogs TEXT NOT NULL,
    contractMarkdown TEXT NOT NULL,
    selectedTemplateName VARCHAR(255),
    templateMarkdown TEXT,
    userId INTEGER,
    sessionId VARCHAR(255),
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_contract_history_session_id ON contract_history(sessionId);
CREATE INDEX IF NOT EXISTS idx_contract_history_user_id ON contract_history(userId);
CREATE INDEX IF NOT EXISTS idx_contract_history_created_at ON contract_history(createdAt);
