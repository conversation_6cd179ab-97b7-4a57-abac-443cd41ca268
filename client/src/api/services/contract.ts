import { v4 as uuid } from 'uuid';

import { API_URL } from '@/shared/config';
import { getTokenFromLocalStorage } from '@/shared/utils/localStorageUtil.ts';
import { parseStrToJson } from '@/shared/utils/stringUtil';
import type { ResultResponse } from '@/types/common';

import api from '../api';

// SSE错误类型枚举
enum SSEErrorType {
  CONNECTION_FAILED = 'connection_failed', // 连接建立失败
  CONNECTION_LOST = 'connection_lost', // 连接中断
  NETWORK_ERROR = 'network_error', // 网络错误
  SERVER_ERROR = 'server_error', // 服务器错误
}

// SSE错误分析工具
const analyzeSSEError = (
  eventSource: EventSource | undefined,
  hasTaskSubmitted: boolean,
): { type: SSEErrorType; message: string; isRecoverable: boolean } => {
  const readyState = eventSource?.readyState;

  if (!hasTaskSubmitted) {
    // 任务未提交阶段的错误
    if (readyState === EventSource.CLOSED) {
      return {
        type: SSEErrorType.CONNECTION_FAILED,
        message: 'SSE连接失败：服务器拒绝连接，请检查网络或稍后重试',
        isRecoverable: false,
      };
    } else {
      return {
        type: SSEErrorType.NETWORK_ERROR,
        message: 'SSE连接失败：无法建立连接，请检查您的网络',
        isRecoverable: false,
      };
    }
  } else {
    // 任务已提交阶段的错误
    if (readyState === EventSource.CLOSED) {
      return {
        type: SSEErrorType.CONNECTION_LOST,
        message: 'SSE连接已断开：您将无法继续收到消息，但任务仍在后台运行，可稍后刷新界面查看结果',
        isRecoverable: true,
      };
    } else {
      return {
        type: SSEErrorType.SERVER_ERROR,
        message: 'SSE连接不稳定：消息传输中断，任务仍在后台运行，建议稍后刷新界面查看结果',
        isRecoverable: true,
      };
    }
  }
};

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  template_markdown: string;
  selected_template_name: string;
  sessionId?: string; // 添加sessionId字段
}

export const contractApi = {
  /**
   * 生成合同预览 - 使用SSE流式接口
   * @param userInput 用户输入
   * @param onLog 日志回调函数
   * @returns 合同生成结果
   */
  async generateContractPreview(
    userInput: string,
    onLog: (log: string) => void,
  ): Promise<ResultResponse<ContractGenerationResult>> {
    return new Promise((resolve) => {
      // 第一步：生成sessionId
      const sessionId = uuid();
      // 获取token并添加到URL中
      const token = getTokenFromLocalStorage();
      // 构建完整URL，包含token
      const url = `${API_URL}contract/stream?sessionId=${sessionId}${token ? `&token=${token}` : ''}`;
      onLog('正在建立SSE连接...');

      // 第二步：建立SSE连接，传递sessionId
      let eventSource = new EventSource(url);
      let haspost = false;

      const disposeEventSource = (result: ResultResponse<ContractGenerationResult>) => {
        eventSource?.close();
        eventSource = undefined!;
        resolve(result);
      };

      eventSource.onopen = () => {
        onLog('SSE连接已建立');
        api.post('contract/generate_contract', { sessionId, userInput }).then((result) => {
          const { success, message } = result as unknown as ResultResponse;
          haspost = success;
          if (!success) {
            onLog(message);
            disposeEventSource({ success: false, message });
          }
        });
      };

      eventSource.onmessage = (event) => {
        const data = parseStrToJson(event.data, {});
        data.log && onLog(data.log);
        if ('success' in data) {
          // 在返回结果中添加sessionId
          if (data.success && data.data) {
            data.data.sessionId = sessionId;
          }
          disposeEventSource(data);
        }
      };

      eventSource.onerror = (event) => {
        console.error('SSE连接错误', event);

        // 使用错误分析工具获取详细的错误信息
        const errorAnalysis = analyzeSSEError(eventSource, haspost);

        console.log('SSE错误分析:', {
          type: errorAnalysis.type,
          isRecoverable: errorAnalysis.isRecoverable,
          hasTaskSubmitted: haspost,
          readyState: eventSource?.readyState,
          sessionId,
        });

        onLog(errorAnalysis.message);
        disposeEventSource({
          success: false,
          message: errorAnalysis.message,
          errorCode: errorAnalysis.isRecoverable ? '1000' : '-1',
        });
      };
    });
  },
};
