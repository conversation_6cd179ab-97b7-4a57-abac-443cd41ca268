import { type ChangeEvent, type FC, type FormEvent, memo } from 'react';

import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FormField } from '@/components/ui/form-field';
import { useObservableState } from '@/shared/hooks/useObservableState';

import type { LoginController } from './LoginController';

interface LoginFormProps {
  controller: LoginController;
}

// 静态头部组件 - 无需任何props
const FormHeader = memo(() => (
  <CardHeader className="space-y-2">
    <CardTitle className="text-center text-3xl font-bold text-primary">合同智能助手</CardTitle>
    <CardDescription className="text-center">输入您的账号和密码继续</CardDescription>
  </CardHeader>
));

// 错误消息组件 - 接收controller直接订阅error状态
const ErrorMessage = memo<{ controller: LoginController }>(({ controller }) => {
  const error = useObservableState(controller.error$);
  if (!error) return null;

  return (
    <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive" role="alert">
      {error}
    </div>
  );
});

// 用户名输入组件 - 接收controller后自行处理相关逻辑
const IdentifierInput = memo<{ controller: LoginController }>(({ controller }) => {
  const identifier = useObservableState(controller.identifier$);
  const isLoading = useObservableState(controller.isLoading$);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    controller.updateIdentifier(e.target.value);
  };

  return (
    <FormField
      id="identifier"
      label="邮箱或用户名"
      value={identifier}
      onChange={handleChange}
      placeholder="请输入邮箱或用户名"
      required
      disabled={isLoading}
    />
  );
});

// 密码输入组件 - 接收controller后自行处理相关逻辑
const PasswordInput = memo<{ controller: LoginController }>(({ controller }) => {
  const password = useObservableState(controller.password$);
  const isLoading = useObservableState(controller.isLoading$);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    controller.updatePassword(e.target.value);
  };

  return (
    <FormField
      id="password"
      label="密码"
      type="password"
      value={password}
      onChange={handleChange}
      placeholder="请输入密码"
      required
      disabled={isLoading}
    />
  );
});

// 登录按钮组件 - 接收controller后自行处理相关逻辑
const SubmitButton = memo<{ controller: LoginController }>(({ controller }) => {
  const isValid = useObservableState(controller.isValid$);
  const isLoading = useObservableState(controller.isLoading$);

  const handleSubmit = () => {
    controller.login();
  };

  return (
    <CardFooter className="flex flex-col">
      <Button
        type="button"
        className="w-full"
        disabled={isLoading || !isValid}
        onClick={handleSubmit}
      >
        {isLoading && <Loader2 className="mr-2 size-4 animate-spin" />}
        登录
      </Button>

      <p className="mt-4 text-center text-sm text-muted-foreground">测试账号: test_mdt / mdt@123</p>
    </CardFooter>
  );
});

// 主容器组件 - 仅负责组合各个子组件
const LoginForm: FC<LoginFormProps> = ({ controller }) => {
  // 表单提交处理 - 仅阻止默认行为
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
  };

  return (
    <Card className="w-full">
      <FormHeader />
      <form onSubmit={handleSubmit}>
        <ErrorMessage controller={controller} />
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <IdentifierInput controller={controller} />
            <PasswordInput controller={controller} />
          </div>
        </CardContent>
        <SubmitButton controller={controller} />
      </form>
    </Card>
  );
};

export default LoginForm;
