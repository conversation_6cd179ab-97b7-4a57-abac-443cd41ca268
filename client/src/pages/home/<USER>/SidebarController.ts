import { BehaviorSubject } from 'rxjs';
import { toast } from 'sonner';

import { contractHistoryApi } from '@/api/services/contract-history';
import type { ContractHistoryListItem } from '@/types/api';

import type { HomeController } from '../HomeController';

export class SidebarController {
  // Sidebar状态
  sideBarCollapsed$ = new BehaviorSubject<boolean>(false);
  historyItems$ = new BehaviorSubject<ContractHistoryListItem[]>([]);
  historyLoading$ = new BehaviorSubject<boolean>(false);

  private homeController: HomeController;

  constructor(homeController: HomeController) {
    this.homeController = homeController;
    // 初始化历史记录
    this.loadHistoryItems();
  }

  // 加载历史记录
  loadHistoryItems = async () => {
    this.historyLoading$.next(true);
    try {
      const response = await contractHistoryApi.getHistoryList({
        page: 1,
        pageSize: 50, // 加载最近50条记录
      });

      if (response.success && response.data) {
        this.historyItems$.next(response.data.items);
      } else {
        toast.error(response.message || '加载历史记录失败');
        this.historyItems$.next([]);
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      toast.error('加载历史记录失败');
      this.historyItems$.next([]);
    } finally {
      this.historyLoading$.next(false);
    }
  };

  // 加载历史记录项目
  loadHistoryItem = async (sessionId: string) => {
    this.historyLoading$.next(true);

    try {
      const result = await contractHistoryApi.getHistoryDetail(sessionId);

      if (result.success && result.data) {
        // 通过HomeController加载历史记录数据
        this.homeController.loadFromHistory(result.data);
        this.sideBarCollapsed$.next(true);
        toast.success('历史记录加载成功');
      } else {
        toast.error(result.message || '加载历史记录失败');
      }
    } catch (error) {
      console.error('加载历史记录详情失败:', error);
      toast.error('加载历史记录失败');
    } finally {
      this.historyLoading$.next(false);
    }
  };

  // 删除历史记录项目
  deleteHistoryItem = async (sessionId: string) => {
    try {
      const result = await contractHistoryApi.deleteHistory(sessionId);

      if (result.success) {
        toast.success('历史记录已删除');
        this.loadHistoryItems();
        return true;
      } else {
        toast.error(result.message || '删除历史记录失败');
        return false;
      }
    } catch (error) {
      console.error('删除历史记录失败:', error);
      toast.error('删除历史记录失败');
      return false;
    }
  };

  // 处理新分析
  handleNewContract = () => {
    // 如果侧边栏已折叠，则展开
    if (this.sideBarCollapsed$.getValue()) {
      this.sideBarCollapsed$.next(false);
    }
    this.homeController.resetToInitialState();
  };

  // 切换侧边栏折叠状态
  toggleSidebarCollapsed = () => {
    const newValue = !this.sideBarCollapsed$.getValue();
    this.sideBarCollapsed$.next(newValue);
  };

  // 展开侧边栏并加载历史记录
  expandAndLoadHistory = () => {
    this.sideBarCollapsed$.next(false);
    this.loadHistoryItems();
  };

  // 销毁
  destroy() {
    // 完成所有观察对象
    this.sideBarCollapsed$.complete();
    this.historyItems$.complete();
    this.historyItems$.next([]);
    this.historyLoading$.complete();

    // 清理引用
    this.homeController = null!;
  }
}
