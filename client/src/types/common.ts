// 结果响应格式
export interface SuccessResponse<T> {
  success: true;
  message: string;
  data: T; // 成功时data一定存在
}

export interface ErrorResponse {
  success: false;
  message: string;
  errorCode?: string;
}

export type ResultResponse<T = undefined> = SuccessResponse<T> | ErrorResponse;

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}
