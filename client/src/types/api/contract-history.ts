// 历史记录列表项接口（对应后端ContractHistoryListItemDto）
export interface ContractHistoryListItem {
  id: number;
  title: string;
  sessionId: string;
  selectedTemplateName: string;
  createdAt: string;
  updatedAt: string;
}

// 历史记录详情接口（对应后端ContractHistory实体）
export interface ContractHistoryDetail {
  id: number;
  title: string;
  userInput: string;
  backendLogs: string;
  contractMarkdown: string;
  selectedTemplateName: string;
  templateMarkdown: string;
  userId: number;
  sessionId: string;
  createdAt: string;
  updatedAt: string;
}

// 历史记录查询参数接口（对应后端HistoryQueryDto）
export interface HistoryQueryParams {
  page?: number;
  pageSize?: number;
  title?: string;
  content?: string;
  templateName?: string;
  startDate?: string;
  endDate?: string;
}

// 更新历史记录标题参数接口
export interface UpdateHistoryTitleParams {
  sessionId: string;
  title: string;
}
