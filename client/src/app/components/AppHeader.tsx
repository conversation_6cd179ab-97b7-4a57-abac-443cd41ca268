import { type FC, memo } from 'react';
import { Link } from 'react-router-dom';

import { ChevronDown, LogOut, Settings } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { VISIBLE_PROMPT_TEMPLATES } from '@/shared/config';
import { useObservableState } from '@/shared/hooks/useObservableState';

import type { AppController } from '../AppController';

// import logoSrc from './logo192.png';

interface AppHeaderProps {
  controller: AppController;
}

// Logo组件 - 纯展示组件
const Logo = memo(() => (
  <div className="flex items-center">
    {/*<img src={logoSrc} alt="Logo" className="mr-2 size-8" />*/}
    <h1 className="text-xl font-bold text-[#1890ff]">合同智能助手</h1>
  </div>
));

// 用户菜单组件 - 仅接收controller后自行处理状态和交互
const UserMenu = memo<{ controller: AppController }>(({ controller }) => {
  const user = useObservableState(controller.user$);

  // 如果用户不存在，不渲染菜单
  if (!user) return null;

  const handleLogout = () => {
    controller.logout();
  };

  const plEle = VISIBLE_PROMPT_TEMPLATES ? (
    <DropdownMenuItem asChild>
      <Link to="/prompt-templates" className="flex items-center">
        <Settings className="mr-2 size-4" />
        <span>提示词设置</span>
      </Link>
    </DropdownMenuItem>
  ) : null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center space-x-2 text-gray-700 transition-colors hover:text-[#1890ff]">
        <span>{user.username}</span>
        <ChevronDown className="size-4" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {plEle}
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 size-4" />
          <span>退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
});

// 主容器组件 - 负责组合子组件
const AppHeader: FC<AppHeaderProps> = ({ controller }) => {
  const isAuthenticated = useObservableState(controller.isAuthenticated$);

  // 如果未认证，不渲染头部
  if (!isAuthenticated || controller.onlyHomePage) return null;

  return (
    <header className="h-16 border-b border-[#f0f0f0] bg-white shadow-sm">
      <div className="flex h-full items-center justify-between px-4">
        <Logo />
        <div className="flex items-center">
          <UserMenu controller={controller} />
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
