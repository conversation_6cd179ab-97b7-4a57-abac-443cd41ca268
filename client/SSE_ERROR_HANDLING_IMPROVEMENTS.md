# SSE错误处理和Sidebar刷新改进

## 问题描述

### 问题1：合同生成完成后Sidebar历史记录不刷新
- **现象**：合同生成成功后，点击"新建"按钮时看不到历史记录，需要刷新页面才能看到
- **原因**：`HomeController.generateContractWithLogCallback`方法中，合同生成成功后没有通知`SidebarController`刷新历史记录列表

### 问题2：SSE连接错误无法区分前端还是后端问题
- **现象**：SSE连接出错时，无法准确判断是网络问题、前端断开还是后端服务问题
- **原因**：原有的`onerror`处理逻辑过于简单，只根据`haspost`状态判断

## 解决方案

### 1. 自动刷新Sidebar历史记录

**修改文件**: `client/src/pages/home/<USER>

在合同生成成功后添加了自动刷新历史记录的逻辑：

```typescript
// 刷新Sidebar历史记录列表
this.sidebarController?.loadHistoryItems();
```

对于可恢复的SSE错误（任务已提交但连接断开），也会延迟刷新历史记录：

```typescript
// 延迟2秒刷新，给后端处理时间
setTimeout(() => {
  this.sidebarController?.loadHistoryItems();
}, 2000);
```

### 2. 改进SSE错误处理

**修改文件**: `client/src/api/services/contract.ts`

#### 新增SSE错误类型枚举：
```typescript
enum SSEErrorType {
  CONNECTION_FAILED = 'connection_failed',    // 连接建立失败
  CONNECTION_LOST = 'connection_lost',        // 连接中断
  NETWORK_ERROR = 'network_error',           // 网络错误
  SERVER_ERROR = 'server_error',             // 服务器错误
}
```

#### 新增错误分析工具函数：
```typescript
const analyzeSSEError = (
  eventSource: EventSource | undefined,
  hasTaskSubmitted: boolean,
  errorEvent: Event
): { type: SSEErrorType; message: string; isRecoverable: boolean }
```

#### 改进的错误处理逻辑：
- **连接建立阶段错误**：标记为不可恢复，提示检查网络
- **任务提交后连接断开**：标记为可恢复，提示任务仍在后台运行
- **详细的错误日志**：包含错误类型、连接状态、会话ID等信息

### 3. 错误类型判断逻辑

| 阶段 | 连接状态 | 错误类型 | 是否可恢复 | 用户提示 |
|------|----------|----------|------------|----------|
| 连接建立前 | CLOSED | CONNECTION_FAILED | ❌ | 服务器拒绝连接，请检查网络 |
| 连接建立前 | 其他 | NETWORK_ERROR | ❌ | 无法建立连接，请检查网络 |
| 任务提交后 | CLOSED | CONNECTION_LOST | ✅ | 连接断开，任务仍在后台运行 |
| 任务提交后 | 其他 | SERVER_ERROR | ✅ | 连接不稳定，任务仍在后台运行 |

## 测试验证

### 测试场景1：正常合同生成
1. 输入合同需求
2. 点击生成按钮
3. 等待生成完成
4. **验证**：Sidebar历史记录自动更新，显示新生成的记录

### 测试场景2：SSE连接建立失败
1. 断开网络连接
2. 尝试生成合同
3. **验证**：显示"SSE连接失败：无法建立连接，请检查您的网络"

### 测试场景3：任务提交后连接断开
1. 开始生成合同
2. 在生成过程中断开网络
3. **验证**：显示"连接断开，任务仍在后台运行"，并延迟刷新历史记录

## 技术细节

### 错误恢复机制
- 可恢复错误会返回`sessionId`，允许用户稍后查看结果
- 自动延迟刷新历史记录，给后端处理时间
- 详细的错误日志便于调试和监控

### 性能优化
- 只在必要时刷新历史记录
- 使用延迟刷新避免频繁请求
- 错误分析函数轻量级，不影响性能
